import 'reflect-metadata';
import { AppModule } from './app';
import { HeronJS } from '@heronjs/core';
import { GlobalErrorInterceptor } from '@interceptors';
import { GlobalErrorInterceptor as PaymentGlobalErrorInterceptor } from '@cbidigital/payment-module/src/interceptors';
import { GlobalErrorInterceptor as MembershipGlobalErrorInterceptor } from '@cbidigital/membership-module/src/interceptors';

const main = async () => {
    const app = await HeronJS.create({ module: AppModule });
    await app.listen({
        port: 3000,
        options: {
            cors: {
                origin: '*',
                preflightContinue: false,
                methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
            },
            globalError: GlobalErrorInterceptor,
            interceptors: [PaymentGlobalErrorInterceptor, MembershipGlobalErrorInterceptor],
        },
    });
};

(async () => main())();
