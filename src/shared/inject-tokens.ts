export const PAYMENT_SERVICE_INJECT_TOKENS = Object.freeze({
    FACTORY: Object.freeze({
        WEBHOOK_HANDLER: 'payment.webhook-handler.factory',
    }),
    HANDLER: Object.freeze({
        INVOICE_CREATED: 'payment.invoice-created.handler',
        PAYMENT_FAILED: 'payment.payment-failed.handler',
        PAYMENT_SUCCEEDED: 'payment.payment-succeeded.handler',
        SUBSCRIPTION_CANCELLED: 'payment.subscription-cancelled.handler',
    }),

    USECASE: Object.freeze({
        HANDLE_WEBHOOK_EVENT: 'handle-webhook-event.usecase',
    }),
});
