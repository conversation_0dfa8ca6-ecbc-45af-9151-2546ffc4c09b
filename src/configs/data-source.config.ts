import { readFileSync } from 'fs';
import { DataSourceClient, DataSourceDriver, CommonDataSourceConfig } from '@heronjs/common';

export const POSTGRES_DATA_SOURCE_CONFIG = {
    client: DataSourceClient.KNEX,
    config: <CommonDataSourceConfig>{
        host: process.env.POSTGRES_HOST || 'payment_postgres',
        port: Number(process.env.POSTGRES_PORT) || 5432,
        user: process.env.POSTGRES_USER || 'dev',
        password: process.env.POSTGRES_PASSWORD || 'dev@123',
        database: process.env.POSTGRES_DATABASE || 'dev',
        pooling: {
            min: Number(process.env.POSTGRES_POOLING_MIN) || 2,
            max: Number(process.env.POSTGRES_POOLING_MAX) || 10,
        },
        driver: DataSourceDriver.POSTGRES,
        // cluster: process.env.POSTGRES_SLAVES
        //     ? {
        //           slaves: process.env.POSTGRES_SLAVES || '',
        //       }
        //     : undefined,
        ssl: process.env.USE_TLS === 'true' ? { rejectUnauthorized: false } : undefined,
        tls: process.env.SSL_CERT
            ? {
                  ssl: readFileSync(process.env.SSL_CERT).toString('utf-8'),
                  enabled: true,
              }
            : undefined,
    },
};
