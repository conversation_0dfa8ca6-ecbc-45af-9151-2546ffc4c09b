import { ZodError } from 'zod';
import StatusCodes from 'http-status-codes';
import { APIError, HttpResponseUtils, Logger } from '@heronjs/common';
import { Next, HttpRequest, HttpResponse, ExpressErrorInterceptor } from '@heronjs/express';

const logger = new Logger('GlobalApiErrorInterceptor');
export const GlobalErrorInterceptor: ExpressErrorInterceptor = (
    originalError: any,
    req: HttpRequest,
    res: HttpResponse,
    next: Next,
) => {
    if (!originalError) return next();
    let err = originalError;
    console.log(err);

    switch (true) {
        case err.constructor.name === 'DatabaseError': {
            err = transformDatabaseError(err);
            break;
        }
        case err.constructor.name === 'ZodError': {
            err = transformZodError(err);
            break;
        }
        default: {
            err = new APIError(StatusCodes.INTERNAL_SERVER_ERROR, 'Internal server error.');
            break;
        }
    }

    return res.send(HttpResponseUtils.error(err));
};

const transformDatabaseError = (err: any) => {
    return err;
};

const transformZodError = (err: ZodError) => {
    const [issue] = err.errors;
    const transformedError = new APIError(StatusCodes.BAD_REQUEST, JSON.stringify(issue));
    return transformedError;
};
