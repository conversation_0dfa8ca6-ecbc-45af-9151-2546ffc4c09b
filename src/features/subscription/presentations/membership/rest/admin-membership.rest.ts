import { PaginationInput, PaginationUtil } from '@cbidigital/aqua-ddd';
import { MEMBERSHIP_INJECT_TOKENS } from '@cbidigital/membership-module';
import { Get, Rest, Body, Fuse, Post, Guard, Param, Patch, Queries, Principal } from '@heronjs/common';
import {
    ICreateMembershipUseCase,
    IUpdateMembershipUseCase,
    IGetMembershipByIdUseCase,
    CreateMembershipUseCaseInput,
    IGetListOfMembershipsUseCase,
    UpdateMembershipUseCaseInput,
} from '@cbidigital/membership-module/src/features/app';

@Rest('/admin/memberships')
export class AdminMembershipRest {
    @Post({ uri: '/', code: 201 })
    @Guard({ private: true })
    async create(
        @Fuse(MEMBERSHIP_INJECT_TOKENS.USECASE.CREATE_MEMBERSHIP)
        useCase: ICreateMembershipUseCase,
        @Principal('sub') authId: string,
        @Body() body: CreateMembershipUseCaseInput,
    ) {
        const result = await useCase.exec(body, { auth: { authId } });
        return result;
    }

    @Patch({ uri: '/:id', code: 200 })
    @Guard({ private: true })
    async update(
        @Fuse(MEMBERSHIP_INJECT_TOKENS.USECASE.UPDATE_MEMBERSHIP)
        useCase: IUpdateMembershipUseCase,
        @Principal('sub') authId: string,
        @Param('id') id: string,
        @Body() body: UpdateMembershipUseCaseInput,
    ) {
        return useCase.exec({ ...body, id }, { auth: { authId } });
    }

    @Get({ uri: '/:id' })
    @Guard({ private: true })
    async getById(
        @Fuse(MEMBERSHIP_INJECT_TOKENS.USECASE.GET_MEMBERSHIP_BY_ID)
        useCase: IGetMembershipByIdUseCase,
        @Principal('sub') authId: string,
        @Param('id') id: string,
    ) {
        return useCase.exec({ id }, { auth: { authId } });
    }

    @Get({ uri: '/' })
    @Guard({ private: true })
    async getList(
        @Principal('sub') authId: string,
        @Queries()
        queries: {
            search?: string;
            offset?: string;
            limit?: string;
            sort?: string;
            filter?: any;
        },
    ) {
        const useCase = <IGetListOfMembershipsUseCase>(
            use('Container').resolve(MEMBERSHIP_INJECT_TOKENS.USECASE.GET_LIST_OF_MEMBERSHIPS)
        );
        const { offset, limit, sort, filter, search } = queries;
        const input: PaginationInput = PaginationUtil.transform({
            offset,
            limit,
            sort,
            filter,
        });
        return useCase.exec({ ...input, search }, { auth: { authId } });
    }
}
