import { HttpRequest } from '@heronjs/express';
import { PAYMENT_SERVICE_INJECT_TOKENS } from '@shared';
import { PaymentGatewayProvider } from '@cbidigital/payment-module';
import { IHandleWebhookEventUseCase } from '@features/subscription/app';
import { Body, Fuse, Param, Post, Rest, Request } from '@heronjs/common';

@Rest('/webhook/:gateway')
export class PaymentWebhook {
    constructor() {}

    @Post({ uri: '/', code: 201 })
    async handle(
        @Fuse(PAYMENT_SERVICE_INJECT_TOKENS.USECASE.HANDLE_WEBHOOK_EVENT)
        useCase: IHandleWebhookEventUseCase,
        @Param('gateway') gateway: PaymentGatewayProvider,
        @Body() body: any,
        @Request() req: HttpRequest,
    ) {
        const headers = req.headers as Record<string, string>;
        const result = await useCase.exec({ body, gateway, headers }, {});
        return result;
    }
}
