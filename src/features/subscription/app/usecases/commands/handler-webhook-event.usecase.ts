import {
    EventPayload,
    PaymentGatewayProvider,
    IPaymentEventParserFactory,
    PAYMENT_MODULE_INJECT_TOKENS,
} from '@cbidigital/payment-module';
import { PAYMENT_SERVICE_INJECT_TOKENS } from '@shared';
import { IUseCase, UseCase, UseCaseContext } from '@cbidigital/aqua-ddd';
import { IWebhookHandlerFactory } from '@features/subscription/app/handlers';
import { ILogger, Inject, Lifecycle, Logger, Provider } from '@heronjs/common';

export type HandleWebhookEventUseCaseInput = EventPayload & {
    gateway: PaymentGatewayProvider;
};
export type HandleWebhookEventUseCaseOutput = void;

export type IHandleWebhookEventUseCase = IUseCase<
    HandleWebhookEventUseCaseInput,
    HandleWebhookEventUseCaseOutput,
    UseCaseContext
>;

@Provider({
    token: PAYMENT_SERVICE_INJECT_TOKENS.USECASE.HANDLE_WEBHOOK_EVENT,
    scope: Lifecycle.Transient,
})
export class HandleWebhookEventUseCase
    extends UseCase<HandleWebhookEventUseCaseInput, HandleWebhookEventUseCaseOutput, UseCaseContext>
    implements IHandleWebhookEventUseCase
{
    private readonly logger: ILogger;
    constructor(
        @Inject(PAYMENT_MODULE_INJECT_TOKENS.FACTORY.PAYMENT_EVENT_PARSER)
        protected readonly paymentEventParserFactory: IPaymentEventParserFactory,
        @Inject(PAYMENT_SERVICE_INJECT_TOKENS.FACTORY.WEBHOOK_HANDLER)
        protected readonly webhookHandlerFactory: IWebhookHandlerFactory,
    ) {
        super();
        this.setMethods(this.processing);
        this.logger = new Logger(this.constructor.name);
    }

    processing = async (input: HandleWebhookEventUseCaseInput) => {
        try {
            // Then process the event
            const paymentEventParser = this.paymentEventParserFactory.get(input.gateway);
            const parsedEvent = await paymentEventParser.parse(input);
            const webhookHandler = this.webhookHandlerFactory.get(parsedEvent.type);
            await webhookHandler.handle(parsedEvent);
        } catch (error) {
            this.logger.error('Failed to handle webhook event', error);
            throw error;
        }
    };
}
