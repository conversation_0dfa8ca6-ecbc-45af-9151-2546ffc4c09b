import { PAYMENT_SERVICE_INJECT_TOKENS } from '@shared';
import { StandardEventType } from '@cbidigital/payment-module';
import { ILogger, Inject, Lifecycle, Logger, Provider } from '@heronjs/common';
import { IWebhookHandler } from '@features/subscription/app/handlers/webhook.handler';

export interface IWebhookHandlerFactory {
    get(type: StandardEventType): IWebhookHandler;
}

@Provider({
    token: PAYMENT_SERVICE_INJECT_TOKENS.FACTORY.WEBHOOK_HANDLER,
    scope: Lifecycle.Singleton,
})
export class WebhookHandlerFactory implements IWebhookHandlerFactory {
    private readonly logger: ILogger;
    private readonly handlers: Map<StandardEventType, IWebhookHandler>;

    constructor(
        @Inject(PAYMENT_SERVICE_INJECT_TOKENS.HANDLER.INVOICE_CREATED)
        protected readonly invoiceCreatedHandler: IWebhookHandler,
        @Inject(PAYMENT_SERVICE_INJECT_TOKENS.HANDLER.PAYMENT_FAILED)
        protected readonly paymentFailedHandler: IWebhookHandler,
        @Inject(PAYMENT_SERVICE_INJECT_TOKENS.HANDLER.PAYMENT_SUCCEEDED)
        protected readonly paymentSucceededHandler: IWebhookHandler,
    ) {
        this.logger = new Logger(this.constructor.name);
        this.handlers = new Map([
            [StandardEventType.INVOICE_CREATED, this.invoiceCreatedHandler],
            [StandardEventType.PAYMENT_FAILED, this.paymentFailedHandler],
            [StandardEventType.PAYMENT_SUCCEEDED, this.paymentSucceededHandler],
        ]);
    }

    get(type: StandardEventType) {
        const handler = this.handlers.get(type);
        if (!handler) {
            this.logger.error(`Event type ${type} is not supported`);
            throw new Error(`Event type ${type} is not supported`);
        }
        return handler;
    }
}
