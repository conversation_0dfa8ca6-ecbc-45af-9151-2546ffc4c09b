import {
    IDatabaseUtil,
    SubscriptionCancelledEvent,
    PAYMENT_MODULE_INJECT_TOKENS,
} from '@cbidigital/payment-module';
import {
    ISubscriptionRepository,
    MEMBERSHIP_INJECT_TOKENS,
    SubscriptionNotFoundError,
    MissingSubscriptionIdError,
} from '@cbidigital/membership-module';
import { IWebhookHandler } from './webhook.handler';
import { PAYMENT_SERVICE_INJECT_TOKENS } from '@shared';
import { ILogger, Inject, Lifecycle, Logger, Provider } from '@heronjs/common';

@Provider({
    token: PAYMENT_SERVICE_INJECT_TOKENS.HANDLER.SUBSCRIPTION_CANCELLED,
    scope: Lifecycle.Singleton,
})
export class SubscriptionCancelledHandler implements IWebhookHandler {
    private readonly logger: ILogger;

    constructor(
        @Inject(MEMBERSHIP_INJECT_TOKENS.REPOSITORY.SUBSCRIPTION)
        protected readonly subsRepo: ISubscriptionRepository,
        @Inject(PAYMENT_MODULE_INJECT_TOKENS.UTIL.DATABASE)
        protected readonly databaseUtil: IDatabaseUtil,
    ) {
        this.logger = new Logger(this.constructor.name);
    }

    async handle(event: SubscriptionCancelledEvent) {
        const { data } = event;
        const { subscription: subscriptionGatewayId } = data;
        if (!subscriptionGatewayId) throw new MissingSubscriptionIdError();
        const trx = await this.databaseUtil.startTrx();
        const repoOptions = { trx };
        try {
            const subsGatewayMapping = await this.subsRepo.findOneGatewayMapping(
                { filter: { subscriptionGatewayId: { $eq: subscriptionGatewayId } } },
                repoOptions,
            );
            if (!subsGatewayMapping) throw new SubscriptionNotFoundError();
            const subscriptionId = subsGatewayMapping.subscriptionId;
            const subscription = await this.subsRepo.findOne(
                { filter: { id: { $eq: subscriptionId } } },
                repoOptions,
            );
            if (!subscription) throw new SubscriptionNotFoundError();
            await subscription.cancel();
            await this.subsRepo.update(subscription, repoOptions);
            await trx.commit();
        } catch (error) {
            await trx.rollback();
            this.logger.error('Failed to handle subscription cancelled', error);
            throw error;
        }
    }
}
