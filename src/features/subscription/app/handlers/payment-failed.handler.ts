import { IWebhook<PERSON>and<PERSON> } from './webhook.handler';
import { PAYMENT_SERVICE_INJECT_TOKENS } from '@shared';
import { RepositoryOptions } from '@cbidigital/aqua-ddd';
import { ILogger, Inject, Lifecycle, Logger, Provider } from '@heronjs/common';
import {
    PaymentEvent,
    IDatabaseUtil,
    IWebhookEventBuilder,
    ITransactionRepository,
    IWebhookEventRepository,
    TransactionNotFoundError,
    PAYMENT_MODULE_INJECT_TOKENS,
} from '@cbidigital/payment-module';
import {
    ISubscriptionRepository,
    MEMBERSHIP_INJECT_TOKENS,
    SubscriptionNotFoundError,
    MissingSubscriptionIdError,
} from '@cbidigital/membership-module';

@Provider({
    token: PAYMENT_SERVICE_INJECT_TOKENS.HANDLER.PAYMENT_FAILED,
    scope: Lifecycle.Singleton,
})
export class PaymentFailedHandler implements IWebhookHandler {
    private readonly logger: ILogger;

    constructor(
        @Inject(PAYMENT_MODULE_INJECT_TOKENS.BUILDER.WEBHOOK_EVENT)
        protected readonly webhookEventBuilder: IWebhookEventBuilder,
        @Inject(PAYMENT_MODULE_INJECT_TOKENS.REPOSITORY.WEBHOOK_EVENT)
        protected readonly webhookEventRepo: IWebhookEventRepository,
        @Inject(MEMBERSHIP_INJECT_TOKENS.REPOSITORY.SUBSCRIPTION)
        protected readonly subsRepo: ISubscriptionRepository,
        @Inject(PAYMENT_MODULE_INJECT_TOKENS.REPOSITORY.TRANSACTION)
        protected readonly transactionRepo: ITransactionRepository,
        @Inject(PAYMENT_MODULE_INJECT_TOKENS.UTIL.DATABASE)
        protected readonly databaseUtil: IDatabaseUtil,
    ) {
        this.logger = new Logger(this.constructor.name);
    }

    async handle(event: PaymentEvent) {
        const trx = await this.databaseUtil.startTrx();
        const repoOptions = { trx };
        try {
            const isHandled = await this.isWebhookEventHandled(event, repoOptions);
            if (isHandled) return;
            await Promise.all([
                this.updateSubscription(event, repoOptions),
                this.updateTransaction(event, repoOptions),
                this.saveWebhookEvent(event, repoOptions),
            ]);
            await trx.commit();
        } catch (error) {
            await trx.rollback();
            this.logger.error('Failed to handle payment failed', error);
            throw error;
        }
    }

    private async isWebhookEventHandled(event: PaymentEvent, repoOptions: RepositoryOptions) {
        return this.webhookEventRepo.findOne(
            {
                filter: { eventId: { $eq: event.eventId }, gateway: { $eq: event.gateway } },
            },
            repoOptions,
        );
    }

    private async saveWebhookEvent(event: PaymentEvent, repoOptions: RepositoryOptions) {
        const webhookEvent = await this.webhookEventBuilder.build();
        await webhookEvent.create({
            eventId: event.eventId,
            gateway: event.gateway,
            eventType: event.type,
            payload: event,
            referenceId: event.data.invoice,
        });
        await this.webhookEventRepo.create(webhookEvent, repoOptions);
    }

    private async updateTransaction(event: PaymentEvent, repoOptions: RepositoryOptions) {
        const { data } = event;
        const { invoice } = data;

        const transaction = await this.transactionRepo.findOne(
            { filter: { gatewayTransactionId: { $eq: invoice } } },
            repoOptions,
        );
        if (!transaction) throw new TransactionNotFoundError();
        transaction.markAsFailed();
        await this.transactionRepo.update(transaction, repoOptions);
    }

    private async updateSubscription(event: PaymentEvent, repoOptions: RepositoryOptions) {
        const { data } = event;
        const { appSubscriptionId } = data.metadata;
        if (!appSubscriptionId) throw new MissingSubscriptionIdError();
        // Handle subscription created
        const subscription = await this.subsRepo.findOne(
            { filter: { id: { $eq: appSubscriptionId } } },
            repoOptions,
        );
        if (!subscription) throw new SubscriptionNotFoundError();
        await subscription.markAsExpired();
        await this.subsRepo.update(subscription, repoOptions);
    }
}
