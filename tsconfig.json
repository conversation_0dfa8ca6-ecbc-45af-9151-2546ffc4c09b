{
    "compilerOptions": {
        "baseUrl": "./",
        "emitDecoratorMetadata": true,
        "esModuleInterop": true,
        "experimentalDecorators": true,
        "declaration": true,
        "importHelpers": true,
        "module": "commonjs",
        "outDir": "dist",
        "removeComments": true,
        "skipLibCheck": true,
        "sourceMap": true,
        "strict": true,
        "target": "es6",
        "moduleResolution": "node",
        "lib": [
            "es6",
            "dom"
        ],
        "types": [
            "node"
        ],
        "typeRoots": [
            "node_modules/@types"
        ],
        "paths": {
            "@context": [
                "src/context"
            ],
            "@features/*": [
                "src/features/*"
            ],
            "@configs": [
                "src/configs"
            ],
            "@interceptors": [
                "src/interceptors"
            ],
            "@shared": [
                "src/shared"
            ]
        },
        "plugins": [
            {
                "transform": "typescript-transform-paths"
            },
            {
                "transform": "typescript-transform-paths",
                "afterDeclarations": true
            }
        ]
    },
    "include": [
        "src/**/*.ts",
        "spec/**/*.ts",
        "node_modules/@cbidigital/**/*.d.ts",
        "src/**/*.html",
    ],
    "exclude": [
        "spec",
        "src/**/*.mock.ts",
        "www/public/"
    ]
}