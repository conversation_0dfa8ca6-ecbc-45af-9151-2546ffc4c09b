image: docker:20.10.5
variables:
  DOCKER_HOST: tcp://docker:2375
  DOCKER_TLS_CERTDIR: ""
  IMAGE_VERSION: $CI_COMMIT_TAG
  NPM_TOKEN: $NPM_TOKEN
services:
  - docker:20.10.5-dind

stages:
  - test
  - build
  - deploy

test:
  stage: test
  only:
    - tags
  when: always
  before_script:
    - apk add --update nodejs npm yarn
  script:
    - echo "Run Unit Test"
#    - yarn
#    - yarn test

build:
  stage: build
  only:
    - tags
  when: on_success
  before_script:
    - echo $CI_COMMIT_TAG
  script:
    - docker build -f ci/Dockerfile --build-arg NPM_TOKEN=$NPM_TOKEN -t $REGISTRY_SERVER/$REGISTRY_TAG:$CI_COMMIT_TAG .
    - echo "$REGISTRY_PASSWORD" | docker login $REGISTRY_SERVER -u $REGISTRY_USER --password-stdin
    - docker push $REGISTRY_SERVER/$REGISTRY_TAG:$CI_COMMIT_TAG

deploy:
  stage: deploy
  only:
    - tags
  variables:
    REGISTRY_TAG: $REGISTRY_TAG
    IMAGE_VERSION: $CI_COMMIT_TAG
  when: on_success
  script:
    - echo "deploy"
