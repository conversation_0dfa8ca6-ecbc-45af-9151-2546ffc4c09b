const TABLE_NAME = 'subs_item_attributes';

const attributes = [
    {
        code: 'tax_code',
        label: 'Tax Code',
        type: 'string',
        status: 'enabled',
        visibility: true,
        sort_order: 1,
        system_defined: false,
        is_required: false,
        editable: true,
        created_at: new Date()
    }
]

exports.seed = async function (knex) {
    for (const attribute of attributes) {
        const isExsited = await knex(TABLE_NAME)
            .select('code')
            .where({ code: attribute.code })
            .first();

        if (isExsited) throw new Error(`attribute with code ${attribute.code} is already existed!`);

        await knex(TABLE_NAME).insert(attribute);
    }
};