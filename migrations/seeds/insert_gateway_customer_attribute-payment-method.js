const TABLE_NAME = 'gateway_customer_attributes';

const attributes = [
    {
        code: 'payment_method',
        label: 'payment method',
        type: 'array',
        status: 'enabled',
        visibility: true,
        sort_order: 1,
        system_defined: false,
        is_required: false,
        editable: true,
        options: null,
        created_at: new Date()
    }
]

exports.seed = async function (knex) {
    for (const attribute of attributes) {
        const isExsited = await knex(TABLE_NAME)
            .select('code')
            .where({ code: attribute.code })
            .first();

        if (isExsited) throw new Error(`attribute with code ${method.gateway_code} is already existed!`);

        await knex(TABLE_NAME).insert(attribute);
    }
};